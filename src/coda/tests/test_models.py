import pytest
from django.test import TestCase
from django.db import models
from django.utils import timezone
from datetime import datetime
import uuid

from coda.models import (
    UUIDModel, 
    CreatedMixin, 
    UpdatedMixin, 
    StartedMixin, 
    CompletedMixin, 
    FailedMixin,
    BaseTimestampedModel,
    BaseModel
)


# Concrete test models for testing abstract mixins
class TestUUIDModel(UUIDModel):
    name = models.Char<PERSON>ield(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestCreatedModel(CreatedMixin):
    name = models.CharField(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestUpdatedModel(UpdatedMixin):
    name = models.CharField(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestStartedModel(StartedMixin):
    name = models.Char<PERSON>ield(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestCompletedModel(CompletedMixin):
    name = models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestFailedModel(FailedMixin):
    name = models.Char<PERSON><PERSON>(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestBaseTimestampedModel(BaseTimestampedModel):
    name = models.CharField(max_length=100)
    
    class Meta:
        app_label = 'coda'


class TestBaseModelConcrete(BaseModel):
    name = models.CharField(max_length=100)
    
    class Meta:
        app_label = 'coda'


@pytest.mark.django_db
class TestUUIDModelMixin(TestCase):
    """Test UUIDModel abstract base class."""
    
    def test_uuid_field_properties(self):
        """Test UUID field properties."""
        instance = TestUUIDModel(name="test")
        
        # Should have UUID as primary key
        self.assertIsInstance(instance.id, uuid.UUID)
        self.assertTrue(instance._meta.get_field('id').primary_key)
        self.assertFalse(instance._meta.get_field('id').editable)
    
    def test_uuid_auto_generation(self):
        """Test that UUID is automatically generated."""
        instance1 = TestUUIDModel.objects.create(name="test1")
        instance2 = TestUUIDModel.objects.create(name="test2")
        
        self.assertIsInstance(instance1.id, uuid.UUID)
        self.assertIsInstance(instance2.id, uuid.UUID)
        self.assertNotEqual(instance1.id, instance2.id)


@pytest.mark.django_db
class TestCreatedMixinModel(TestCase):
    """Test CreatedMixin."""
    
    def test_created_at_auto_set(self):
        """Test that created_at is automatically set on creation."""
        before_creation = timezone.now()
        instance = TestCreatedModel.objects.create(name="test")
        after_creation = timezone.now()
        
        self.assertIsNotNone(instance.created_at)
        self.assertGreaterEqual(instance.created_at, before_creation)
        self.assertLessEqual(instance.created_at, after_creation)
    
    def test_created_property(self):
        """Test created property convenience accessor."""
        instance = TestCreatedModel.objects.create(name="test")
        self.assertEqual(instance.created, instance.created_at)
    
    def test_created_at_not_editable(self):
        """Test that created_at field is not editable."""
        field = TestCreatedModel._meta.get_field('created_at')
        self.assertFalse(field.editable)
    
    def test_created_at_not_updated_on_save(self):
        """Test that created_at doesn't change on subsequent saves."""
        instance = TestCreatedModel.objects.create(name="test")
        original_created = instance.created_at
        
        # Update and save
        instance.name = "updated"
        instance.save()
        instance.refresh_from_db()
        
        self.assertEqual(instance.created_at, original_created)


@pytest.mark.django_db
class TestUpdatedMixinModel(TestCase):
    """Test UpdatedMixin."""
    
    def test_updated_at_auto_set_on_creation(self):
        """Test that updated_at is automatically set on creation."""
        before_creation = timezone.now()
        instance = TestUpdatedModel.objects.create(name="test")
        after_creation = timezone.now()
        
        self.assertIsNotNone(instance.updated_at)
        self.assertGreaterEqual(instance.updated_at, before_creation)
        self.assertLessEqual(instance.updated_at, after_creation)
    
    def test_updated_at_auto_updated_on_save(self):
        """Test that updated_at is automatically updated on save."""
        instance = TestUpdatedModel.objects.create(name="test")
        original_updated = instance.updated_at
        
        # Wait a bit and update
        import time
        time.sleep(0.01)  # Small delay to ensure different timestamp
        
        instance.name = "updated"
        instance.save()
        instance.refresh_from_db()
        
        self.assertGreater(instance.updated_at, original_updated)
    
    def test_updated_property(self):
        """Test updated property convenience accessor."""
        instance = TestUpdatedModel.objects.create(name="test")
        self.assertEqual(instance.updated, instance.updated_at)


@pytest.mark.django_db
class TestStartedMixinModel(TestCase):
    """Test StartedMixin."""
    
    def test_started_at_default_null(self):
        """Test that started_at defaults to null."""
        instance = TestStartedModel.objects.create(name="test")
        self.assertIsNone(instance.started_at)
    
    def test_started_at_can_be_set(self):
        """Test that started_at can be manually set."""
        start_time = timezone.now()
        instance = TestStartedModel.objects.create(name="test")
        instance.started_at = start_time
        instance.save()
        instance.refresh_from_db()
        
        self.assertEqual(instance.started_at, start_time)
    
    def test_started_property(self):
        """Test started property convenience accessor."""
        instance = TestStartedModel.objects.create(name="test")
        start_time = timezone.now()
        instance.started_at = start_time
        
        self.assertEqual(instance.started, start_time)


@pytest.mark.django_db
class TestCompletedMixinModel(TestCase):
    """Test CompletedMixin."""
    
    def test_completed_at_default_null(self):
        """Test that completed_at defaults to null."""
        instance = TestCompletedModel.objects.create(name="test")
        self.assertIsNone(instance.completed_at)
    
    def test_completed_at_can_be_set(self):
        """Test that completed_at can be manually set."""
        completion_time = timezone.now()
        instance = TestCompletedModel.objects.create(name="test")
        instance.completed_at = completion_time
        instance.save()
        instance.refresh_from_db()
        
        self.assertEqual(instance.completed_at, completion_time)
    
    def test_completed_property(self):
        """Test completed property convenience accessor."""
        instance = TestCompletedModel.objects.create(name="test")
        completion_time = timezone.now()
        instance.completed_at = completion_time
        
        self.assertEqual(instance.completed, completion_time)


@pytest.mark.django_db
class TestFailedMixinModel(TestCase):
    """Test FailedMixin."""
    
    def test_failed_at_default_null(self):
        """Test that failed_at defaults to null."""
        instance = TestFailedModel.objects.create(name="test")
        self.assertIsNone(instance.failed_at)
    
    def test_failed_at_can_be_set(self):
        """Test that failed_at can be manually set."""
        failure_time = timezone.now()
        instance = TestFailedModel.objects.create(name="test")
        instance.failed_at = failure_time
        instance.save()
        instance.refresh_from_db()
        
        self.assertEqual(instance.failed_at, failure_time)


@pytest.mark.django_db
class TestBaseTimestampedModelMixin(TestCase):
    """Test BaseTimestampedModel (combines Created and Updated mixins)."""
    
    def test_has_both_timestamps(self):
        """Test that BaseTimestampedModel has both created_at and updated_at."""
        instance = TestBaseTimestampedModel.objects.create(name="test")
        
        self.assertIsNotNone(instance.created_at)
        self.assertIsNotNone(instance.updated_at)
    
    def test_timestamps_behavior(self):
        """Test that both timestamps work correctly together."""
        instance = TestBaseTimestampedModel.objects.create(name="test")
        original_created = instance.created_at
        original_updated = instance.updated_at
        
        # Small delay and update
        import time
        time.sleep(0.01)
        
        instance.name = "updated"
        instance.save()
        instance.refresh_from_db()
        
        # created_at should not change, updated_at should change
        self.assertEqual(instance.created_at, original_created)
        self.assertGreater(instance.updated_at, original_updated)


@pytest.mark.django_db
class TestBaseModelMixin(TestCase):
    """Test BaseModel (combines BaseTimestampedModel and UUIDModel)."""
    
    def test_has_uuid_and_timestamps(self):
        """Test that BaseModel has UUID primary key and timestamps."""
        instance = TestBaseModelConcrete.objects.create(name="test")
        
        # Should have UUID primary key
        self.assertIsInstance(instance.id, uuid.UUID)
        
        # Should have timestamps
        self.assertIsNotNone(instance.created_at)
        self.assertIsNotNone(instance.updated_at)
    
    def test_complete_model_functionality(self):
        """Test complete BaseModel functionality."""
        instance = TestBaseModelConcrete.objects.create(name="test")
        original_id = instance.id
        original_created = instance.created_at
        
        # Update the instance
        import time
        time.sleep(0.01)
        instance.name = "updated"
        instance.save()
        instance.refresh_from_db()
        
        # ID and created_at should not change
        self.assertEqual(instance.id, original_id)
        self.assertEqual(instance.created_at, original_created)
        
        # updated_at should change
        self.assertGreater(instance.updated_at, original_created)
