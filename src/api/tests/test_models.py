import pytest
from django.test import TestCase
from django.db import models

from api.models import AsyncRequest, AsyncRequestStatus


class ConcreteAsyncRequest(AsyncRequest):
    """Concrete implementation for testing the abstract AsyncRequest model."""
    name = models.Char<PERSON>ield(max_length=100)
    
    def execute(self):
        return "executed"


class TestAsyncRequestStatus(TestCase):
    """Test AsyncRequestStatus choices."""
    
    def test_status_choices(self):
        """Test that all expected status choices are available."""
        expected_choices = ["pending", "completed", "failed", "cancelled"]
        actual_choices = [choice[0] for choice in AsyncRequestStatus.choices]
        
        for choice in expected_choices:
            self.assertIn(choice, actual_choices)
    
    def test_status_values(self):
        """Test specific status values."""
        self.assertEqual(AsyncRequestStatus.PENDING, "pending")
        self.assertEqual(AsyncRequestStatus.COMPLETED, "completed")
        self.assertEqual(AsyncRequestStatus.FAILED, "failed")
        self.assertEqual(AsyncRequestStatus.CANCELLED, "cancelled")


@pytest.mark.django_db
class TestAsyncRequest(TestCase):
    """Test AsyncRequest abstract model."""
    
    def setUp(self):
        """Set up test data."""
        self.request = ConcreteAsyncRequest.objects.create(name="test_request")
    
    def test_default_status(self):
        """Test that default status is PENDING."""
        self.assertEqual(self.request.status, AsyncRequestStatus.PENDING)
    
    def test_is_pending_property(self):
        """Test is_pending property."""
        # Should be True for pending status
        self.assertTrue(self.request.is_pending)
        
        # Should be False for other statuses
        self.request.status = AsyncRequestStatus.COMPLETED
        self.assertFalse(self.request.is_pending)
    
    def test_is_complete_property(self):
        """Test is_complete property."""
        # Should be False for pending status
        self.assertFalse(self.request.is_complete)
        
        # Should be True for completed status
        self.request.status = AsyncRequestStatus.COMPLETED
        self.assertTrue(self.request.is_complete)
    
    def test_execute_method(self):
        """Test that execute method can be overridden."""
        result = self.request.execute()
        self.assertEqual(result, "executed")
    
    def test_status_field_properties(self):
        """Test status field properties."""
        status_field = ConcreteAsyncRequest._meta.get_field('status')
        self.assertEqual(status_field.max_length, 20)
        self.assertEqual(status_field.default, AsyncRequestStatus.PENDING)
        self.assertTrue(status_field.db_index)
    
    def test_status_transitions(self):
        """Test status transitions."""
        # Start with pending
        self.assertEqual(self.request.status, AsyncRequestStatus.PENDING)
        
        # Change to completed
        self.request.status = AsyncRequestStatus.COMPLETED
        self.request.save()
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, AsyncRequestStatus.COMPLETED)
        
        # Change to failed
        self.request.status = AsyncRequestStatus.FAILED
        self.request.save()
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, AsyncRequestStatus.FAILED)
