import pytest
from django.test import TestCase
from datetime import datetime
from django.utils import timezone

from estate.models import Customer, AffiliateVendor, Partner


@pytest.mark.django_db
class TestCustomer(TestCase):
    """Test Customer model."""
    
    def setUp(self):
        """Set up test data."""
        self.vendor = AffiliateVendor.objects.create(
            vendorIdentity="test_vendor",
            domain="test.com"
        )
        self.partner = Partner.objects.create(
            name="Test Partner"
        )
    
    def test_customer_creation(self):
        """Test basic customer creation."""
        customer = Customer.objects.create(
            name="Test Customer",
            affiliatedVendor="Test Vendor",
            partnerAccountId="12345",
            customerSecret="secret123",
            customerGUID="guid123"
        )
        
        self.assertEqual(customer.name, "Test Customer")
        self.assertEqual(customer.affiliatedVendor, "Test Vendor")
        self.assertEqual(customer.partnerAccountId, "12345")
        self.assertEqual(customer.customerSecret, "secret123")
        self.assertEqual(customer.customerGUID, "guid123")
    
    def test_customer_with_foreign_keys(self):
        """Test customer creation with foreign key relationships."""
        customer = Customer.objects.create(
            name="Test Customer",
            vendorId=self.vendor,
            partnerId=self.partner
        )
        
        self.assertEqual(customer.vendorId, self.vendor)
        self.assertEqual(customer.partnerId, self.partner)
    
    def test_get_s3_settings(self):
        """Test get_s3_settings method."""
        customer = Customer.objects.create(
            name="Test Customer",
            s3region="us-east-1",
            s3key="test_key",
            s3secret="test_secret",
            s3bucket="test_bucket",
            s3folder="test_folder"
        )
        
        s3_settings = customer.get_s3_settings()
        
        expected_settings = {
            "region_name": "us-east-1",
            "aws_access_key_id": "test_key",
            "aws_secret_access_key": "test_secret",
            "bucket_name": "test_bucket",
            "folder_name": "test_folder"
        }
        
        self.assertEqual(s3_settings, expected_settings)
    
    def test_get_s3_settings_with_none_values(self):
        """Test get_s3_settings method with None values."""
        customer = Customer.objects.create(name="Test Customer")
        
        s3_settings = customer.get_s3_settings()
        
        expected_settings = {
            "region_name": None,
            "aws_access_key_id": None,
            "aws_secret_access_key": None,
            "bucket_name": None,
            "folder_name": None
        }
        
        self.assertEqual(s3_settings, expected_settings)
    
    def test_customer_defaults(self):
        """Test customer default values."""
        customer = Customer.objects.create(name="Test Customer")
        
        self.assertEqual(customer.storage, "op")
        self.assertEqual(customer.sftp, 0)
        self.assertEqual(customer.customerType, 1)
        self.assertEqual(customer.queueRunning, 0)
        self.assertEqual(customer.defaultCallbackURL, "https://umony.com")
    
    def test_customer_meta_options(self):
        """Test Customer model meta options."""
        self.assertFalse(Customer._meta.managed)
        self.assertEqual(Customer._meta.db_table, "customer")
        self.assertEqual(Customer._meta.default_permissions, ("view",))


@pytest.mark.django_db
class TestAffiliateVendor(TestCase):
    """Test AffiliateVendor model."""
    
    def test_affiliate_vendor_creation(self):
        """Test basic affiliate vendor creation."""
        vendor = AffiliateVendor.objects.create(
            vendorIdentity="test_vendor",
            domain="example.com",
            notes="Test notes"
        )
        
        self.assertEqual(vendor.vendorIdentity, "test_vendor")
        self.assertEqual(vendor.domain, "example.com")
        self.assertEqual(vendor.notes, "Test notes")
    
    def test_affiliate_vendor_with_datetime(self):
        """Test affiliate vendor with created datetime."""
        created_time = timezone.now()
        vendor = AffiliateVendor.objects.create(
            vendorIdentity="test_vendor",
            created=created_time
        )
        
        self.assertEqual(vendor.created, created_time)
    
    def test_affiliate_vendor_meta_options(self):
        """Test AffiliateVendor model meta options."""
        self.assertFalse(AffiliateVendor._meta.managed)
        self.assertEqual(AffiliateVendor._meta.db_table, "affiliate_vendor")
        self.assertEqual(AffiliateVendor._meta.default_permissions, ("view",))


@pytest.mark.django_db
class TestPartner(TestCase):
    """Test Partner model."""
    
    def test_partner_creation(self):
        """Test basic partner creation."""
        partner = Partner.objects.create(
            name="Test Partner",
            maxTrailDuration=30
        )
        
        self.assertEqual(partner.name, "Test Partner")
        self.assertEqual(partner.maxTrailDuration, 30)
    
    def test_partner_with_dates(self):
        """Test partner with datetime fields."""
        created_time = timezone.now()
        contract_start = timezone.now()
        
        partner = Partner.objects.create(
            name="Test Partner",
            created=created_time,
            contractStartDate=contract_start
        )
        
        self.assertEqual(partner.created, created_time)
        self.assertEqual(partner.contractStartDate, contract_start)
    
    def test_partner_defaults(self):
        """Test partner default values."""
        partner = Partner.objects.create(name="Test Partner")
        
        self.assertEqual(partner.name, "Test Partner")
        self.assertIsNone(partner.maxTrailDuration)
        self.assertIsNone(partner.created)
        self.assertIsNone(partner.contractStartDate)
    
    def test_partner_meta_options(self):
        """Test Partner model meta options."""
        self.assertFalse(Partner._meta.managed)
        self.assertEqual(Partner._meta.db_table, "partner")
        self.assertEqual(Partner._meta.default_permissions, ("view",))
